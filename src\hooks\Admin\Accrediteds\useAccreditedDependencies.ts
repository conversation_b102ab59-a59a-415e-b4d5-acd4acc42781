import { useQuery } from '@tanstack/react-query';
import { api } from '~/services/apiClient';

type AccreditedsDependenciesResponse = {
  specialties: { secure_id: string; name: string }[];
  exams: { secure_id: string; name: string }[];
  states?: { name: string; uf: string }[];
  state?: { name: string; uf: string }[]; 
};

type GetAccreditedsDependenciesResponse = {
  specialtiesAndExamsOptions: { label: string; value: string }[];
  statesOptions: { label: string; value: string }[];
};

export async function getAccreditedsDependencies(): Promise<GetAccreditedsDependenciesResponse> {
  const response = await api.get<AccreditedsDependenciesResponse>('/v1/admin/accrediteds-dependencies');
  const dependencies = response.data;

  const statesArray = dependencies?.states || dependencies?.state || [];

  const formattedSpecialties = (dependencies?.specialties || []).map((specialty) => ({
    label: specialty.name,
    value: specialty.secure_id,
  }));

  const formattedExams = (dependencies?.exams || []).map((exam) => ({
    label: exam.name, 
    value: exam.secure_id,
  }));
  
  const formattedStates = statesArray.map((state) => ({
    label: state.name,
    value: state.uf,
  }));

  return {
    specialtiesAndExamsOptions: [
      { label: 'Selecione uma especialidade/exame', value: 'Selecione uma especialidade/exame' }, 
      ...formattedSpecialties,
      ...formattedExams,
    ],
    statesOptions: [
      { label: 'Selecione um estado', value: 'Selecione um estado' }, 
      ...formattedStates,
    ],
  };
}

export function useAccreditedsDependencies() {
  return useQuery(['accreditedsDependencies'], getAccreditedsDependencies);
}
