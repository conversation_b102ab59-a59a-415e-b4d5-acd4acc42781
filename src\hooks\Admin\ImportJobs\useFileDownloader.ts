import { useState } from 'react';
import { useToast } from '@chakra-ui/react';
import { api } from '~/services/apiClient';

export function useFileDownloader() {
  const [isDownloading, setIsDownloading] = useState(false);
  const toast = useToast();

  const downloadFile = async ({ url, filename }: { url: string; filename: string }) => {
    setIsDownloading(true);
    try {
      const response = await api.get(url, {
        responseType: 'blob',
      });

      const href = URL.createObjectURL(response.data);

      const link = document.createElement('a');
      link.href = href;
      link.setAttribute('download', filename);
      document.body.appendChild(link);

      link.click();

      document.body.removeChild(link);
      URL.revokeObjectURL(href);

      toast({
        title: 'Download iniciado!',
        status: 'success',
        isClosable: true,
      });

    } catch (error) {
      toast({
        title: 'Falha no download',
        description: 'Não foi possível baixar o relatório de erros.',
        status: 'error',
        isClosable: true,
      });
    } finally {
      setIsDownloading(false);
    }
  };

  return { isDownloading, downloadFile };
}