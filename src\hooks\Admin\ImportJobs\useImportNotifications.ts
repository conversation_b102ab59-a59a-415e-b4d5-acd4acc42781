import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { api } from '~/services/apiClient';

export function useImportNotifications() {
  const queryClient = useQueryClient();

  const { data, ...rest } = useQuery(
    ['importJobSummary'],
    async () => {
      const { data } = await api.get('v1/admin/import-jobs/summary');
      return data;
    },
    {
      // Busca a cada 15 segundos se a janela do navegador estiver em foco
      refetchInterval: 15000,
    }
  );

  const markAsRead = useMutation(
    async (jobId) => {
      await api.post(`v1/admin/import-jobs/${jobId}/mark-as-read`);
    },
    {
      // Ao marcar como lida, invalida a query para a lista de notificações atualizar
      onSuccess: () => {
        queryClient.invalidateQueries(['importJobSummary']);
      },
    }
  );

  const hasNotifications = (data?.processingJobs?.length > 0) || (data?.recentFinishedJobs?.length > 0);

  return {
    summary: data,
    hasNotifications,
    markAsRead,
    ...rest,
  };
}