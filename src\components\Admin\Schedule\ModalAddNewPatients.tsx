import {
	Button,
	Divider,
	Flex,
	Grid,
	GridItem,
	HStack,
	Modal,
	ModalBody,
	ModalContent,
	Modal<PERSON>ooter,
	ModalHeader,
	ModalOverlay,
	Stack,
	useToast,
} from "@chakra-ui/react"
import { GetServerSideProps, NextPage } from "next"

import * as yup from "yup"
import { SubmitHandler, useForm } from "react-hook-form"

import { AxiosError, AxiosResponse } from "axios"
import { useMutation } from "@tanstack/react-query"
import { yupResolver } from "@hookform/resolvers/yup"

import { api } from "~/services/apiClient"
import { Option } from "~/utils/Types/Global"
import { queryClient } from "~/services/queryClient"
import { UserFormData } from "~/utils/Types/Admin/User"
import { WithSSRAuth } from "~/utils/Validator/WithSSRAuth"

import { FormUser } from "~/components/global/FormUser"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { InputPassword } from "~/components/global/Form/InputPassword"
import { Input } from "~/components/global/Form/Input"
import { InputAsyncSelect } from "~/components/global/Form/InputAsyncSelect"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { cpfValidation } from "~/utils/Validator/validateCpf"
import { FC, useEffect, useState } from "react"

import type { GetListPatientByCPFResponseDTO } from "~/dto/patients/listByCpf.dto"

type FormData = UserFormData & {
	origin: string
	partnerSecureId: Option
	isActive: 'true' | 'false'
}

const FormSchema = yup.object().shape({
	userExists: yup.boolean(),
	name: yup.string().required("Nome obrigatório"),
	email: yup.string().required("E-mail obrigatório").email("E-mail inválido"),
	password: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema
			.required("Senha obrigatória")
			.min(6, "No mínimo 6 caracteres")
	}),
	passwordConfirmation: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.oneOf([yup.ref("password")], "As senha precisam ser iguais")
	}),
	legal_document_number: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("CPF obrigatório")
	})
	.test("cpf-validator", "CPF inválido", (value) => {
		if (!value) return false;

		const cleanedValue = value.replace(/\D/g, "");

		return cpfValidation(cleanedValue);
	}),
	cell: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("Celular obrigatório")
	}),
	birth_date: yup.string().when('userExists', {
		is: (value: boolean) => !value,
		then: (schema) => schema.required("Data de nascimento obrigatória")
	}),
	isActive: yup.mixed<'true' | 'false'>().oneOf(['true', 'false']),
})

interface ModalAddNewPatients {
	isOpen: boolean
	onClose: () => void
	onPatientCreated?: (patientData: GetListPatientByCPFResponseDTO) => void
	initialCpf?: string 
}

const ModalPatientsAdd: FC<ModalAddNewPatients> = ({ isOpen, onClose, onPatientCreated, initialCpf  }) => {
	const toast = useToast()

	const {
		register,
		formState,
		formState: { errors },
		handleSubmit,
		watch,
		setValue,
		clearErrors,
		control,
		reset
	} = useForm<FormData>({
		//@ts-ignore
		resolver: yupResolver(FormSchema),
		defaultValues: {
			userExists: false,
			isActive: 'true'
		},
	})

	const handleSearchPartners = async (search: string) => {
		const { data } = await api.get('v1/admin/list/partners', {
			params: {
				search,
			}
		})

		const partners = data.map((user: any) => ({ label: user.userInfo.name, value: user.secure_id }))

		return partners
	}

	useEffect(() => {
		if (isOpen) {
			if (initialCpf) {
				const formattedCpf = initialCpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4')
				reset({
					legal_document_number: formattedCpf,
				})
			} else {
				reset({
					legal_document_number: '',
				})
			}
		}
	}, [isOpen, initialCpf, reset])

	// Faz a busca do paciente recém-criado
	const fetchCreatedPatient = async (cpf: string): Promise<GetListPatientByCPFResponseDTO | null> => {
		try {
			const cleanedCpf = cpf.replace(/\D/g, "");
			const response = await api.get<GetListPatientByCPFResponseDTO[]>(
				`v1/admin/list/patients-by-cpf/patient/${cleanedCpf}`
			);
			
			return response.data.length > 0 ? response.data[0] : null;
		} catch (error) {
			console.error("Erro ao buscar paciente recém-criado:", error);
			return null;
		}
	};

	const add = useMutation(
		async (values: FormData) => {
			const newCell = values.cell.replace(/\D/g, '')
			const ddd_cell = newCell.slice(0, 2)
			const cell = newCell.slice(2)

			return await api.post("/v1/admin/patients", {
				...values,
				ddd_cell,
				cell,
				partnerSecureId: values.partnerSecureId ? values.partnerSecureId.value : undefined,
				isActive: values.isActive === 'true' ? true : false
			})
		},
		{
			onSuccess: async (response: AxiosResponse, variables: FormData) => {
				queryClient.invalidateQueries(["PatientsAdmin"])
		
				if (onPatientCreated) {
					const patientFromApi = await fetchCreatedPatient(variables.legal_document_number);
					
					if (patientFromApi) {
						onPatientCreated(patientFromApi);
					} else {
						const patientData: GetListPatientByCPFResponseDTO = {
							patientData: {
								secureId: response.data.patient?.secureId || response.data.secureId,
								name: response.data.patient?.name || variables.name,
								legalDocument: response.data.patient?.legalDocument || variables.legal_document_number,
								email: response.data.patient?.email || variables.email,
								partnerName: response.data.patient?.partnerName || null,
							},
							addressData: {
								zipCode: response.data.patient?.address?.zipCode || null,
								street: response.data.patient?.address?.street || null,
								streetNumber: response.data.patient?.address?.streetNumber || null,
								complement: response.data.patient?.address?.complement || null,
								neighborhood: response.data.patient?.address?.neighborhood || null,
								city: response.data.patient?.address?.city || null,
								state: response.data.patient?.address?.state || null,
							},
							dependentsData: response.data.patient?.dependents || []
						};

						onPatientCreated(patientData);
					}
				}

				reset()
				onClose()
			},
			onError: (error: AxiosError<any>) => {
				toast({
					title:
						error?.response?.data?.message ||
						"Ocorreu um problema ao cadastrar paciente.",
					position: "top-right",
					status: error?.response?.data?.type || "error",
					isClosable: true,
				})
			},
		}
	)

	const handleAdd: SubmitHandler<FormData> = async (values) => {
		try {
			await add.mutateAsync(values)
		} catch {}
	}

	const handleClose = () => {
		reset({
			userExists: false,
			isActive: 'true',
			legal_document_number: ''
		})
		onClose()
	}

	return (
		<Modal size={"2xl"} isOpen={isOpen} onClose={handleClose}>
			<ModalOverlay />
			<ModalContent
				as="form"
				overflowY={"hidden"}
				onSubmit={handleSubmit(handleAdd)}
			>
				<ModalHeader>
					Cadastrar Novo Paciente
				</ModalHeader>
				
				<ModalBody>
					<FormUser
						clearErrors={clearErrors}
						formState={formState}
						register={register as any}
						setValue={setValue as any}
						watch={watch as any}
						textUserExists="já possui cadastro na plataforma, para adiciona-lo como paciente clique em cadastrar."
					/>
					<Grid
						templateColumns={{
							sm: 'repeat(4, 1fr)',
							md: 'repeat(8, 1fr)',
							lg: 'repeat(10, 1fr)',
							xl: 'repeat(1fr, 1fr)',
							'2xl': 'repeat(12, 1fr)',
						}}
						gap={6}
						w="100%"
					>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
							<Divider />
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }}>
							<Input
								placeholder="Origem"
								label="Origem"
								error={errors.origin}
								{...register("origin")}
							/>
						</GridItem>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, '2xl': 6 }}>
							<InputAsyncSelect
								isMulti={false}
								isClearable
								name="partnerSecureId"
								label="Parceiro"
								placeholder="Procure um parceiro"
								control={control}
								error={errors.partnerSecureId}
								handleSearch={handleSearchPartners}
							/>
						</GridItem>
					</Grid>

					<Grid
						templateColumns={{
							sm: 'repeat(4, 1fr)',
							md: 'repeat(8, 1fr)',
							lg: 'repeat(10, 1fr)',
							xl: 'repeat(12, 1fr)',
							'2xl': 'repeat(12, 1fr)',
						}}
						gap={6}
						w="100%"
					>
						<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 12, '2xl': 12 }}>
							<Divider />
						</GridItem>

						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 5, '2xl': 5 }}>
							<InputPassword
								label="Senha *"
								placeholder="Senha *"
								error={errors.password}
								{...register("password")}
							/>
						</GridItem>

						<GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 5, '2xl': 5 }}>
							<InputPassword
								label="Confirmação de Senha *"
								placeholder="Repetir a Senha *"
								error={errors.passwordConfirmation}
								{...register("passwordConfirmation")}
							/>
						</GridItem>

						<GridItem colSpan={{ sm: 4, md: 8, lg: 10, xl: 2, '2xl': 2 }}>
							<InputSelect
								label="Status"
								options={[
									{ label: "Ativo", value: "true" },
									{ label: "Inativo", value: "false" },
								]}
								{...register("isActive")}
							/>
						</GridItem>
					</Grid>
				</ModalBody>

				<ModalFooter>
					<HStack spacing={"4"}>
						<ButtonCancelSubmit onClick={handleClose}>
							Cancelar
						</ButtonCancelSubmit>
						<ButtonSubmit 
							type="submit"
							p={4}
							isLoading={formState.isSubmitting}
						>
							Cadastrar
						</ButtonSubmit>
					</HStack>
				</ModalFooter>
			</ModalContent>
		</Modal>
	)
}

export default ModalPatientsAdd
