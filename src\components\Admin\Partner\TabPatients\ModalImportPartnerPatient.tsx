import {
	Box,
	Button,
	Flex,
	HStack,
	Modal,
	ModalBody,
	ModalCloseButton,
	ModalContent,
	ModalHeader,
	ModalOverlay,
	Progress,
	Text,
	VStack,
	useToast
} from "@chakra-ui/react"
import { FC, useEffect, useMemo, useState } from "react"

import { parse } from "date-fns"

import * as yup from 'yup'
import { AxiosError, AxiosResponse } from "axios"
import { useMutation, useQuery } from "@tanstack/react-query"
import { yupResolver } from "@hookform/resolvers/yup"
import { SubmitHandler, useForm } from "react-hook-form"

import { read, utils } from 'xlsx'

import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { InputDocFile } from "~/components/global/Form/InputDocFile"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonExportExcel } from "~/components/global/Buttons/ButtonExportExcel"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { chunk } from "lodash"
import { useFileDownloader } from "~/hooks/Admin/ImportJobs/useFileDownloader"

const FormSchema = yup.object().shape({
	file: yup.mixed().required('Você deve importar o arquivo Excel'),
	// users: yup.array().of(yup.object({
	// 	name: yup.string().required(),
	// 	email: yup.string().required(),
	// 	legalDocumentNumber: yup.string().required(),
	// 	dddCell: yup.number().required(),
	// 	cell: yup.number().required(),
	// 	birthDate: yup.string().required(),
	// 	adviceRegister: yup.string().required(),
	// 	paymentMethods: yup.string().required(),
	// 	typeOfCare: yup.mixed<'in_person' | 'video_call' | 'both'>().oneOf(['in_person', 'video_call', 'both']).required(),
	// 	type: yup.mixed<'doctor' | 'clinic' | 'hospital' | 'lab'>().oneOf(['doctor', 'clinic', 'hospital', 'lab']).required()
	// }).required()).optional()
})

type FormData = {
	file: File | null
	// users?: {
	// 	name: string
	// 	email: string
	// 	legalDocumentNumber: string
	// 	dddCell: number
	// 	cell: number
	// 	birthDate: string
	// 	password?: string
	// 	holderLegalDocumentNumber?: string
	// }[]
}

interface ModalImportPartnerPatientProps {
	isOpen: boolean
	closeModal: () => void
	partnerId?: string
	initialJobId?: string | number | null
	progressOnly?: boolean
}

const ImportProgress = ({ jobId, jobStatus }: any) => {
	const { isDownloading, downloadFile } = useFileDownloader();

	const handleDownload = () => {
    if (!jobId) {
      alert('ID do Job não encontrado!');
      return;
    }

    downloadFile({
      url: `/v1/admin/import-jobs/${jobId}/download-errors`,
      filename: `erros_importacao_${jobId}.xlsx`,
    });
  };

	if (!jobStatus) return null;

	const { processedRows = 0, totalRows = 0, status } = jobStatus;
	const percentage = totalRows > 0 ? Math.round((processedRows / totalRows) * 100) : 0;

	let statusText = 'Iniciando importação...';
	if (status === 'processing') {
		statusText = `Processando ${processedRows} de ${totalRows} registros...`;
	} else if (status === 'completed') {
		statusText = `Importação concluída com sucesso!`;
	} else if (status === 'failed') {
		statusText = `A importação falhou.`;
	} else if (status === 'failed_with_errors') {
		statusText = `A importação foi concluída com erros. Algumas linhas podem conter erros.`;
	}

	return (
		<Box w="100%" p={4}>
			<Text mb={2} fontSize="lg">{statusText}</Text>
			<Progress 
				value={percentage} 
				size="lg" 
				colorScheme={status.includes('failed') ? 'red' : 'green'}
				hasStripe={status === 'processing'}
				isAnimated={status === 'processing'}
				borderRadius="md"
			/>
			<Text textAlign="right" mt={1} fontSize="sm">{percentage}%</Text>
			{jobStatus.status === 'failed_with_errors' && (
				<Flex justify="center" mt={2} w="100%">
					<Button
						colorScheme="red"
						onClick={handleDownload}
						isLoading={isDownloading}
						loadingText="Baixando..."
						w="100%"
					>
						Baixar Relatório de Erros
					</Button>
				</Flex>
			)}
		</Box>
	);
};

export const ModalImportPartnerPatient: FC<ModalImportPartnerPatientProps> = ({ closeModal, isOpen, partnerId, initialJobId, progressOnly }) => {
	const toast = useToast()

	const [jobId, setJobId] = useState<string | null>(initialJobId ? String(initialJobId) : null);
  const [isUploading, setIsUploading] = useState(false);

	useEffect(() => {
		if (initialJobId) {
			setJobId(String(initialJobId));
		}
	}, [initialJobId, isOpen]);

	const { data: jobStatus, isFetching: isCheckingStatus } = useQuery(
		['importJobStatus', jobId],
		async () => {
			const { data } = await api.get(`v1/admin/import-jobs/${jobId}/status`);
			return data;
		},
		{
			enabled: !!jobId, // A query só é ativada quando existe um jobId
			refetchInterval: (data) =>
				(data?.status === 'processing' || data?.status === 'pending') ? 3000 : false,
			
			onSuccess: (data) => {
				if (data.status === 'completed' || data.status === 'failed' || data.status === 'failed_with_errors') {
					queryClient.invalidateQueries(['PartnerPatientsAdmin', partnerId]);

					toast({
						title: data.status === 'completed' ? 'Importação Concluída!' : 'Importação Finalizada',
						description: data.status === 'completed' ? `${data.processedRows} pacientes importados.` : 'Algumas linhas podem conter erros.',
						status: data.status === 'completed' ? 'success' : 'warning',
						duration: 7000,
						isClosable: true,
					});
				}
			},
			onError: () => {
				toast({ title: 'Erro ao verificar status da importação.', description: 'Tente fechar e abrir o modal novamente.', status: 'error' });
				setJobId(null); // Para o polling em caso de erro
			}
		}
	);

	const [hasHolderError, setHasHolderError] = useState(false)

	const { handleSubmit, register, formState, watch, control, reset, setValue } = useForm<FormData>({
		resolver: yupResolver<any>(FormSchema),
		defaultValues: {
		}
	})
	const { errors } = formState

	const excelModel = useMemo(() => {
		return new Array(1).fill(null).map((_, index) => ({
			'Nome': `Usuário`,
			'E-mail': `usuario${index + 1}@exemplo.com.br`,
			'CPF': `000.000.000-00`,
			'Celular': '(00)00000-0000',
			'Data de Nascimento/Fundação (##/##/####)': '##/##/####',
			'Senha (Não obrigatório)': 'Não obrigatório',
			'CPF titular (Não obrigatório)': 'Não obrigatório',
		}))
	}, [])

	const startImport = useMutation(
    async (values: { file: File }) => {
      setIsUploading(true);
      const formData = new FormData();
      formData.append('file', values.file);

      const response = await api.post(`/v1/admin/partners/${partnerId}/patients`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      return response.data;
    },
    {
      onSuccess: (data) => {
        setIsUploading(false);
        setJobId(data.jobId);
        toast({
          title: 'Arquivo enviado!',
          description: 'A importação foi iniciada em segundo plano.',
          status: 'info',
        });
      },
      onError: (error: AxiosError<any>) => {
        setIsUploading(false);
        toast({
          title: 'Erro no Upload!',
          description: error?.response?.data?.message || 'Não foi possível enviar o arquivo.',
          status: "error",
        })
      },
    }
  );

	const handleStartImport: SubmitHandler<FormData> = async (values) => {
    if (!values.file) return;
    await startImport.mutateAsync({ file: values.file });
  };

	// const excelErrors = useMemo(() => {
	// 	if (users) {
	// 		setHasHolderError(false)
	// 		const legalDocumentNumbers = new Set();
	// 		return users.map(user => {
	// 			const hasHolderError = user.holderLegalDocumentNumber
	// 				&& !legalDocumentNumbers.has(user.holderLegalDocumentNumber)
	// 			setHasHolderError(!!hasHolderError)
	// 			legalDocumentNumbers.add(user.legalDocumentNumber)

	// 			return {
	// 				'Nome': user.name,
	// 				'E-mail': user.email,
	// 				'CPF': user.legalDocumentNumber,
	// 				'Celular': user.dddCell + '-' + user.cell,
	// 				'Data de Nascimento/Fundação (##/##/####)': user.birthDate,
	// 				'Senha (Não obrigatório)': user.password,
	// 				'CPF titular (Não obrigatório)': user.holderLegalDocumentNumber,
	// 				'Erros': hasHolderError ? 'CPF titular não existe na base' : ''
	// 			}
	// 		})
	// 	}

	// 	return []
	// }, [users])

	useEffect(() => {
		if (hasHolderError) {
			toast({
				title: `Erro na importação!`,
				position: "top-right",
				status: "error",
				isClosable: true,
			})
		}
	}, [hasHolderError])

  const handleCloseModal = () => {
    reset({ file: null })
    setJobId(null)
    queryClient.removeQueries(['importJobStatus']);
    closeModal()
  }

	const isProcessing = startImport.isLoading || jobStatus?.status === 'pending' || jobStatus?.status === 'processing';
  const isFinished = jobStatus?.status === 'completed' || jobStatus?.status === 'failed' || jobStatus?.status === 'failed_with_errors';

	// useEffect(() => {
	// 	if (file) {
	// 		const LIMITE_EM_BYTES = 10 * 1024 * 1024; // 10MB (o mesmo do back-end)

	// 		if (file.size > LIMITE_EM_BYTES) {
	// 			toast({
	// 				title: "Arquivo muito grande!",
	// 				description: `O tamanho máximo permitido é de 10MB.`,
	// 				status: "error",
	// 				position: "top-right",
	// 				isClosable: true,
	// 			});
	// 			setValue("file", null);
	// 			setValue("users", undefined);
	// 			return;
	// 		}
	// 		readExcel(file)
	// 	}
	// }, [file, setValue, toast])

	return (
		<Modal size="md" isOpen={isOpen} onClose={handleCloseModal} closeOnOverlayClick={false}>
			<ModalOverlay />
			<ModalContent
			>
				<ModalHeader>Importar</ModalHeader>
				<ModalCloseButton />
				<ModalBody>
					<VStack
						as="form"
						width="100%"
						p="4"
						spacing={["6", "8"]}
						flexDirection="column"
						align="flex-start"
						justify="center"
						onSubmit={handleSubmit(handleStartImport)}
					>
						{/* Upload UI: hidden when progressOnly is true or when a job is already in progress */}
					{!jobId && !progressOnly && (
							<>
								<ButtonExportExcel
									variant="link"
									isDisabled={excelModel.length === 0}
									data={excelModel}
									fileName={`modelo_importar_pacientes`}
								>
									Baixar modelo
								</ButtonExportExcel>
								<InputDocFile
									watch={watch}
									control={control}
									error={errors.file}
									{...register('file')}
								/>
							</>
						)}

						{jobId && <ImportProgress jobId={jobId} jobStatus={jobStatus} />}
						
						<Flex justify={"center"} w="100%">
							<HStack spacing="4" width="20em">
								<ButtonCancelSubmit onClick={handleCloseModal}>Cancelar</ButtonCancelSubmit>
								{!progressOnly && (
									<ButtonSubmit isLoading={formState.isSubmitting} isDisabled={ hasHolderError || isProcessing || isFinished}>Salvar</ButtonSubmit>
								)}
							</HStack>
						</Flex>
					</VStack>
				</ModalBody>
			</ModalContent>
		</Modal>
	)
}
