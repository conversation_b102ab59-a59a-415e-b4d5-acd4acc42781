import { useQuery } from "@tanstack/react-query";
import { api } from "~/services/apiClient";
import { masks } from "~/utils/Functions/Masks";
import { ListUserAdmin } from "~/utils/Types/Admin/User";

type GetDoctorsResponse = {
  total: number;
  page: number;
  lastPage: number;
  perPage: number;
  accrediteds: ListUserAdmin[];
};

type GetAccreditedsProps = {
  page: number;
  search?: string;
  limit?: number;

  status?: "active" | "inactive" | "punctual" | "all";
  stateUF?: string;
  cityName?: string;
  specialtySecureId?: string;
  examSecureId?: string;
};

export async function getAccreditedsAdmin({
  page,
  search,
  limit,
  status,

  stateUF,
  cityName,
  specialtySecureId,
  examSecureId,
}: GetAccreditedsProps): Promise<GetDoctorsResponse> {
  const formattedStatus = status === "all" ? undefined : status;

  const receivedSpecialtySecureId =
    specialtySecureId &&
    specialtySecureId.toLocaleLowerCase() !==
      "selecione uma especialidade/exame"
      ? specialtySecureId
      : undefined;

  const receivedExamSecureId =
    examSecureId &&
    examSecureId.toLocaleLowerCase() !== "selecione uma especialidade/exame"
      ? examSecureId
      : undefined;

  const response = await api.get("/v1/admin/accrediteds", {
    params: {
      search,
      page,
      limit,
      status: formattedStatus,
      stateUF:
        stateUF && stateUF.toLocaleLowerCase() !== "selecione um estado"
          ? stateUF
          : undefined,
      examOrSpecialtySecureId:
        receivedExamSecureId !== undefined
          ? receivedExamSecureId
          : receivedSpecialtySecureId,
      cityName:
        cityName && cityName.toLocaleLowerCase() !== "selecione uma cidade"
          ? cityName
          : undefined,
    },
  });

  const accrediteds = response.data.data.map((user: any) => ({
    secure_id: user.secure_id,
    name: user.userInfo.name,
    email: user.email,
    legal_document_number: user.userInfo.legal_document_number,
    cell: masks("cellPhone", `${user.userInfo.ddd_cell}${user.userInfo.cell}`),
    type: user.type,
    show_accredited_in_app: user.show_accredited_in_app,
    status: user.userInfo.status,
  }));

  return {
    total: response.data.meta.total,
    perPage: response.data.meta.per_page,
    page: response.data.meta.current_page,
    lastPage: response.data.meta.last_page,
    accrediteds,
  };
}

export function useAccreditedsAdmin({
  page,
  search,
  limit,
  status,
  stateUF,
  cityName,
  specialtySecureId,
  examSecureId,
}: GetAccreditedsProps) {
  return useQuery(
    [
      "AccreditedsAdmin",
      page,
      search,
      limit,
      status,
      stateUF,
      cityName,
      specialtySecureId,
      examSecureId,
    ],
    () =>
      getAccreditedsAdmin({
        page,
        search,
        limit,
        status,
        stateUF,
        cityName,
        specialtySecureId,
        examSecureId,
      })
  );
}
