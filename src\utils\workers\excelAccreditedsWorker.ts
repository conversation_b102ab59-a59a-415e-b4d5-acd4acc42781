import { read, utils } from 'xlsx';
import { parse } from 'date-fns';

interface ExcelRow {
  'Nome': string;
  'E-mail': string;
  'CNPJ/CPF': string;
  'Celular': string | number;
  'Data de Nascimento/Fundação (##/##/####)': string | number;
  'Registro'?: string;
  'Métodos de Pagamento'?: string;
  'Tipo de Atendimento (Presencial, Vídeo, Ambos)': string;
  'Tipo (Médico, Clínica, Hospital, Laboratório)': string;
  '<PERSON>ha (Não obrigatório)'?: string;
  'CEP'?: string;
  'Endereço'?: string;
  'Número'?: string | number;
  'Complemento'?: string;
  'Bairro'?: string;
  'Cidade'?: string;
  'Estado'?: string;
  'Valor'?: string | number;
  'Especialidades (Separadas por vírgula)'?: string;
  'Exames (Separados por vírgula)'?: string;
}

self.onmessage = (event) => {
  const file = event.data;
  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      const bufferArray = e.target?.result;

      const wb = read(bufferArray, {
        type: 'file',
        cellDates: true,
        cellText: false,
      });

      const wsname = wb.SheetNames[0];

      if (!wsname) {
        self.postMessage({ success: false, error: "Nenhuma aba encontrada na planilha do Excel." });
        return;
      }

      const ws = wb.Sheets[wsname];

      const ref = ws['!ref'];
      if (!ref) {
        // Se a aba está vazia, não há dados para processar. Retorna sucesso com um array vazio.
        self.postMessage({ success: true, data: [] });
        return;
      }

      // Força colunas específicas a serem tratadas como texto para evitar corrupção de números grandes (como em celulares).
      const range = utils.decode_range(ref);
      let celularColumnIndex = -1;

      for (let C = range.s.c; C <= range.e.c; ++C) {
        const address = utils.encode_cell({ r: range.s.r, c: C });
        const cell = ws[address];
        if (cell && cell.v === 'Celular') {
          celularColumnIndex = C;
          break;
        }
      }

      if (celularColumnIndex !== -1) {
        for (let R = range.s.r + 1; R <= range.e.r; ++R) { // Começa da segunda linha (r + 1)
          const address = utils.encode_cell({ r: R, c: celularColumnIndex });
          const cell = ws[address];

          //Se a célula existir e for um número (t: 'n'), converte para texto (t: 's')
          if (cell && cell.t === 'n') {
            cell.t = 's';
            // A biblioteca usa 'w' para o texto formatado e 'v' para o valor.
            // Garantimos que ambos sejam a versão em string do número.
            cell.w = String(cell.v);
            cell.v = String(cell.v);
          }
        }
      }

      const jsonData = utils.sheet_to_json(ws, { raw: false, dateNF: 'DD/MM/YYYY' }) as ExcelRow[];

      const mappedData = jsonData.map((user) => {
        const celularRaw = user['Celular'] || '';
        const newCell = celularRaw.toString().replace(/\D/g, "");
        const dddCell = newCell.slice(0, 2);
        const cell = newCell.slice(2);

        const valorRaw = user['Valor'] || '0';
        const valorNum = Number(valorRaw.toString().replace(",", ".")) || 0;

        const dataNascRaw = user['Data de Nascimento/Fundação (##/##/####)'];
        let birthDate = null;
        try {
          if (dataNascRaw) birthDate = parse(dataNascRaw.toString(), 'dd/MM/yyyy', new Date()).toISOString();
        } catch {
          birthDate = dataNascRaw;
        }

        return {
          name: user['Nome'],
          email: user['E-mail'],
          legalDocumentNumber: user['CNPJ/CPF'],
          dddCell,
          cell,
          birthDate: birthDate,
          adviceRegister: user['Registro'],
          paymentMethods: user['Métodos de Pagamento'],
          typeOfCare: user['Tipo de Atendimento (Presencial, Vídeo, Ambos)'],
          type: user['Tipo (Médico, Clínica, Hospital, Laboratório)'],
          password: user['Senha (Não obrigatório)'],
          zipCode: user['CEP'],
          street: user['Endereço'],
          number: user['Número'],
          complement: user['Complemento'],
          neighborhood: user['Bairro'],
          city: user['Cidade'],
          state: user['Estado'],
          queryValue: valorNum,
          specialtiesNames: user['Especialidades (Separadas por vírgula)'],
          examsNames: user['Exames (Separados por vírgula)']
        };
      });

      // Envia os dados processados de volta para a thread principal.
      self.postMessage({ success: true, data: mappedData });

    } catch (error: any) {
      self.postMessage({ success: false, error: error?.message });
    }
  };

  reader.readAsArrayBuffer(file);
};