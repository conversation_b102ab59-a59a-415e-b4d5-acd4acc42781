import {
	Box,
	Flex,
	Grid,
	HStack,
	Icon,
	IconButton,
	Image,
	Menu,
	MenuButton,
	MenuList,
	Text,
	useBreakpointValue
} from '@chakra-ui/react'
import Router, { useRouter } from 'next/router'
import { FC, useState } from 'react'

import { RiMenuLine } from 'react-icons/ri'
import { IoIosArrowDown } from 'react-icons/io'

import { queryClient } from '~/services/queryClient'
import { useAuthContext } from '~/contexts/AuthContext'
import { useSidebarDrawer } from '~/contexts/SideBarDrawerContext'
import { LuBell } from "react-icons/lu";

import { MenuItem } from './MenuItem'
import { NotificationBell } from './NotificationBell'
import { ModalImportPartnerPatient } from '../Partner/TabPatients/ModalImportPartnerPatient'

export const HeaderAdmin: FC = () => {
	const [isImportModalOpen, setImportModalOpen] = useState(false);
  const [selectedJobId, setSelectedJobId] = useState<number | null>(null);
	const [selectedPartnerId, setSelectedPartnerId] = useState<string | undefined>(undefined);
	const router = useRouter();
  const currentRoute = router.asPath.split("/")[1];
	const { signOut, user } = useAuthContext()
	const { onOpen, isOpen } = useSidebarDrawer()

	const isWideVersion = useBreakpointValue({
		base: false,
		xl: true,
	})

	const handleNotificationClick = (jobId: number, partnerId?: string) => {
    setSelectedJobId(jobId);
    setSelectedPartnerId(partnerId);
    setImportModalOpen(true);
  };
  
  const handleOpenUploadModal = () => {
    setSelectedJobId(null); // Garante que vai abrir em modo de upload
    setImportModalOpen(true);
  }

	return (
		<Flex
			as="header"
			w="100%"
			h="20"
			px="6"
			ml="auto"
			shadow="md"
			bg="primary"
			justifyContent="space-between"
			align="center"
			top="0"
			position="sticky"
			zIndex={2}
			opacity="1"
			visibility="visible"
		>
			{!isWideVersion && (
				<IconButton
					aria-label="Open navigation"
					icon={<Icon as={RiMenuLine} />}
					fontSize="24"
					variant="unstyled"
					onClick={onOpen}
					mr="2"
				/>
			)}

			<Image
				src="/hellomed2.png"
				alt="SA Varejo"
				maxW="120px"
				maxH="50px"
				objectFit="contain"
			/>
			<Flex align="center" gap={4}>
				<NotificationBell onNotificationClick={handleNotificationClick} />
				<Grid
					gridTemplateColumns="1fr 1.5rem"
					alignItems="center"
					height="20"
					gap="2"
				>
					<Box>
						<Text
							textStyle="headerMD"
							color="secondaryText"
							noOfLines={1}
							as="header"
						>
							{user?.name}
						</Text>
						<Text
							textStyle="textXS"
							color="secondaryText"
						>
							Meus dados
						</Text>
					</Box>
					<Menu>
						{({ isOpen: isOpenMenu }) => (
							<>
								<MenuButton>
									<Icon
										as={IoIosArrowDown}
										fontSize="24"
										cursor="pointer"
										color="secondaryText"
									/>
								</MenuButton>
								{isOpenMenu && (
									<MenuList mt="7">
										<MenuItem onClick={() => Router.push(`/${currentRoute}/profile`)}>
											Meus dados
										</MenuItem>
										<MenuItem onClick={() => {
											queryClient.clear()
											signOut()
										}}>
											Sair
										</MenuItem>
									</MenuList>
								)}
							</>
						)}
					</Menu>
				</Grid>
			</Flex>
			{isImportModalOpen && (
        <ModalImportPartnerPatient
          isOpen={isImportModalOpen}
          closeModal={() => setImportModalOpen(false)}
          initialJobId={selectedJobId}
          partnerId={selectedPartnerId}
          progressOnly
        />
      )}
		</Flex >
	)
}
