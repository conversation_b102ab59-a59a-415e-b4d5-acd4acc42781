{"name": "hellomed-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "deploy": "git pull && yarn && yarn build"}, "dependencies": {"@chakra-ui/react": "^2.6.1", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.1.0", "@tanstack/react-query": "^4.29.7", "@types/node": "^20.8.10", "@types/react": "^18.2.33", "@types/react-dom": "18.2.4", "axios": "^1.4.0", "broadcast-channel": "^5.1.0", "chakra-react-select": "4.6.0", "date-fns": "2.30.0", "eslint": "8.40.0", "eslint-config-next": "13.4.2", "framer-motion": "^10.12.12", "jodit-react": "^1.3.39", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.2", "lodash": "^4.17.21", "moment": "^2.30.1", "next": "^13.4.2", "nookies": "^2.5.2", "react": "18.2.0", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.43.9", "react-icons": "^4.8.0", "react-input-mask": "^2.0.4", "react-number-format": "^5.3.1", "twilio-video": "^2.2.0", "typescript": "5.0.4", "xlsx": "^0.18.5", "yup": "^1.1.1"}, "devDependencies": {"@types/react-input-mask": "^3.0.2"}}