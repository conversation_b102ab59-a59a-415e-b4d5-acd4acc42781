import {
	<PERSON>,
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	Td,
	Text,
	Tr,
	useDisclosure,
	useToast,
} from "@chakra-ui/react"
import { FC, useEffect, useRef, useState } from "react"

import { Submit<PERSON>and<PERSON>, useForm } from "react-hook-form"
import { format, parseISO } from "date-fns"
import { useMutation } from "@tanstack/react-query"
import { AxiosError, AxiosResponse } from "axios"

import { RiEyeLine, RiPencilLine } from "react-icons/ri"
import { BsInfoCircle } from "react-icons/bs";

import { useCan } from "~/hooks/useCan"
import { api } from "~/services/apiClient"
import { queryClient } from "~/services/queryClient"

import { PopoverComponent } from "~/components/global/Popover"
import { ModalActionAppointment } from "./ModalActionAppointment"
import { InputSelect } from "~/components/global/Form/InputSelect"
import { ButtonSubmit } from "~/components/global/Buttons/ButtonSubmit"
import { ButtonItemList } from "~/components/global/Buttons/ButtonItemList"
import { ButtonCancelSubmit } from "~/components/global/Buttons/ButtonCancelSubmit"
import { ObservationAppointmentModal } from "./ObservationAppointmentModal"
import { getAppointmentCurrentStatus } from "~/utils/translateNames/appointment/currentStatus"

type FormData = {
	currentStatus: string
	status: string
	labelStatus: string
}

const getStatus = (status: string): string => {
	switch (status) {
		case 'approved':
			return 'Aprovado'

		case 'did_not_attend':
			return 'Não compareceu'

		case 'realized':
			return 'Realizado'

		case 'finalized':
			return 'Finalizado'

		case 'canceled_by_patient':
			return 'Cancelado pelo Paciente'

		case 'canceled_at_patient_request':
			return 'Cancelado a pedido do Paciente'

		case 'canceled_by_backoffice':
			return 'Cancelado pelo Backoffice'

		default:
			return ''
	}
}


interface CardAppointmentAdminProps {
	secure_id: string
	date: string
	patient: string
	specialtyOrExam: string
	currentStatus: 'open' | 'closed'
	status: 'approved' | 'did_not_attend' | 'realized' | 'finalized' | 'canceled_by_Patient' | 'canceled_at_patient_request' | 'canceled_by_backoffice'

	createdAt: string;
	scheduleSecureId: string;
}

export const CardAppointmentAdmin: FC<CardAppointmentAdminProps> = ({ 
	secure_id, 
	date, 
	patient, 
	specialtyOrExam,
	currentStatus,
	status,

	createdAt,
	scheduleSecureId
}) => {
	const toast = useToast()
	const { isOpen, onToggle } = useDisclosure()

	const { 
		isOpen: isObservationModalOpen,
		onClose: onCloseObservationModal,
		onOpen: onOpenObservationModal,
		onToggle: onToggleObservationModal
	} = useDisclosure();

	const { 
		isOpen: isCurrentStatusOpen,
		onClose: onCloseCurrentStatus,
		onOpen: onOpenCurrentStatus,
		onToggle: onToggleCurrentStatus
	} = useDisclosure();

	const { formState, handleSubmit, watch, setValue, register } = useForm<FormData>({
		defaultValues: {
			status,
			labelStatus: getStatus(status)
		}
	})

	const statusSelected = watch('status')
	const labelStatus = watch('labelStatus')

	const userCanSeeEdit = useCan({
		permissions: ['appointment_edit']
	})

	const userHasPermissionView = useCan({
		permissions: ['action_logs_view']
	})

	const [isOpenPopover, setIsOpenPopover] = useState(false)
	const firstFieldRef = useRef(null)

	function onClosePopover() {
		setIsOpenPopover(false)
	}

	function onOpenPopover() {
		setIsOpenPopover(true)
	}

	const changeStatus = useMutation(async (values: FormData) => {
		return await api.put(`/v1/admin/appointments/${secure_id}`, {
			status: values.status
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['AppointmentAdmin'])
			toast({
				title: response.data?.message || 'Status do agendamento alterado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			setValue('status', status)
			toast({
				title: error?.response?.data?.message || 'Erro ao alterar status do agendamento.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	const changeCurrentStatus = useMutation(async (values: FormData) => {
		return await api.put(`/v1/admin/appointments/${secure_id}`, {
			currentStatus: values.currentStatus
		})
	}, {
		onSuccess: (response: AxiosResponse) => {
			queryClient.invalidateQueries(['AppointmentAdmin'])
			toast({
				title: response.data?.message || 'Status atual alterado com sucesso!',
				position: "top-right",
				status: response.data?.type || "success",
				isClosable: true,
			})
		},
		onError: (error: AxiosError<any>) => {
			setValue('currentStatus', currentStatus)
			toast({
				title: error?.response?.data?.message || 'Erro ao alterar status atual.',
				position: "top-right",
				status: error?.response?.data?.type || "error",
				isClosable: true,
			})
		}
	})

	const handleChangeStatus: SubmitHandler<FormData> = async (values) => {
		try {
			await changeStatus.mutateAsync(values)
			onClosePopover()
		} catch {}
	}

	const handleChangeCurrentStatus: SubmitHandler<FormData> = async (values) => {
		try {
			await changeCurrentStatus.mutateAsync(values)
			onClosePopover()
		} catch {}
	}

	useEffect(() => {
		setValue('labelStatus', getStatus(statusSelected))
	}, [statusSelected])

	return (
		<Tr>
			<Td>
				<Text fontSize="sm">{format(parseISO(date), 'dd/MM/yyyy')} ás {format(parseISO(date), 'HH:mm')}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{format(parseISO(createdAt), 'dd/MM/yyyy')} ás {format(parseISO(createdAt), 'HH:mm')}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{patient}</Text>
			</Td>
			<Td>
				<Text fontSize="sm">{specialtyOrExam}</Text>
			</Td>
			<Td>
				<HStack justify="space-between">
					<Text fontSize="sm">{getAppointmentCurrentStatus(currentStatus)}</Text>
					<PopoverComponent
						isOpen={isCurrentStatusOpen}
						initialFocusRef={firstFieldRef}
						onOpen={onOpenCurrentStatus}
						onClose={onCloseCurrentStatus}
						title="Alterar status atual"
						placement="auto"
						body={
							<Flex
								as="form"
								flexDirection="column"
								onSubmit={handleSubmit(handleChangeCurrentStatus)}
							>
								<InputSelect
									label="Status atual"
									options={[
										{ label: 'Aberto', value: 'open' },
										{ label: 'Encerrado', value: 'closed' },
									]}
									{...register('currentStatus')}
								/>

								<PopoverFooter display="flex" justifyContent="flex-end" pt="5" mt="2">
									<Flex justify="flex-end" w="100%">
										<HStack spacing="4" width="20em">
											<ButtonCancelSubmit onClick={() => {
												setValue('currentStatus', currentStatus)
												onClosePopover()
											}}>
												Cancelar
											</ButtonCancelSubmit>
											<ButtonSubmit isLoading={formState.isSubmitting}>
												Alterar
											</ButtonSubmit>
										</HStack>
									</Flex>
								</PopoverFooter>
							</Flex>
						}
					>
						<Box>
							{/* <PopoverAnchor>
								<Text fontSize="sm">{labelStatus}</Text>
							</PopoverAnchor> */}

							{userCanSeeEdit && (
								<PopoverTrigger>
									<IconButton size="sm" aria-label="Edit status" icon={<RiPencilLine />}/>
								</PopoverTrigger>
							)}
						</Box>
					</PopoverComponent>
				</HStack>
			</Td>

			<Td>
				<HStack justify="space-between">
					<Text fontSize="sm">{labelStatus}</Text>
					<PopoverComponent
						isOpen={isOpenPopover}
						initialFocusRef={firstFieldRef}
						onOpen={onOpenPopover}
						onClose={onClosePopover}
						title="Alterar sub-status"
						// tooltipLabel="Alterar status"
						placement="auto"
						body={
							<Flex
								as="form"
								flexDirection="column"
								onSubmit={handleSubmit(handleChangeStatus)}
							>
								<InputSelect
									label="Sub-Status"
									options={[
										{ label: 'Aprovado', value: 'approved' },
										{ label: 'Não compareceu', value: 'did_not_attend' },
										{ label: 'Realizado', value: 'realized' },
										{ label: 'Finalizado', value: 'finalized' },
										{ label: 'Cancelado pelo Paciente', value: 'canceled_by_patient' },
										{ label: 'Cancelado a pedido do Paciente', value: 'canceled_at_patient_request' },
										{ label: 'Cancelado pelo Backoffice', value: 'canceled_by_backoffice' },
									]}
									{...register('status')}
								/>

								<PopoverFooter display="flex" justifyContent="flex-end" pt="5" mt="2">
									<Flex justify="flex-end" w="100%">
										<HStack spacing="4" width="20em">
											<ButtonCancelSubmit onClick={() => {
												setValue('status', status)
												onClosePopover()
											}}>
												Cancelar
											</ButtonCancelSubmit>
											<ButtonSubmit isLoading={formState.isSubmitting}>
												Alterar
											</ButtonSubmit>
										</HStack>
									</Flex>
								</PopoverFooter>
							</Flex>
						}
					>
						<Box>
							{/* <PopoverAnchor>
								<Text fontSize="sm">{labelStatus}</Text>
							</PopoverAnchor> */}

							{userCanSeeEdit && (
								<PopoverTrigger>
									<IconButton size="sm" aria-label="Edit status" icon={<RiPencilLine />} isDisabled={status === "realized" || status === "did_not_attend"} />
								</PopoverTrigger>
							)}
						</Box>
					</PopoverComponent>
				</HStack>
			</Td>
			{userHasPermissionView && (
				<>
					<Td position='sticky' right='0' backgroundColor='white' paddingRight='1rem'>
						<Box w="100%" display='flex' flexDir='row' gap={2}>
							<ButtonItemList
								icon={RiEyeLine}
								colorScheme="telegram"
								tooltipLabel="Visualizar Histórico"
								onClick={onToggle}
							/>
							
							<ButtonItemList
								icon={BsInfoCircle}
								colorScheme="telegram"
								tooltipLabel="Visualizar Observações"
								onClick={onOpenObservationModal}
							/>

							{isOpen && (
								<ModalActionAppointment
									isOpen={isOpen}
									closeModal={onToggle}
									appointmentSecureId={secure_id}
									scheduleSecureId={scheduleSecureId} 
								/>
							)}
							
							{isObservationModalOpen && (
								<ObservationAppointmentModal
									isOpen={isObservationModalOpen}
									onClose={onCloseObservationModal}
									onToggle={onToggleObservationModal}
									scheduleSecureId={scheduleSecureId}
								/>
							)}
						</Box>
					</Td>
				</>
			)}
		</Tr>
	)
}
