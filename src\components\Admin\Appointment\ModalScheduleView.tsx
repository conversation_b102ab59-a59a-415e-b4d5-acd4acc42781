import {
  BoxProps,
  Divider,
  Flex,
  Grid,
  GridItem,
  HStack,
  Image,
  Link,
  Stack,
  Table,
  TableContainer,
  Tbody,
  Td,
  Text,
  Th,
  Thead,
  Tr,
  VStack,
  Spinner,
  Center,
  Alert,
  AlertIcon,
	Box,
} from "@chakra-ui/react";
import { useQuery } from "@tanstack/react-query";
import { FC, useCallback } from "react";

import { api } from "~/services/apiClient";
import {
  FormatDateForDayMonthYearUsingBars,
  FormatDateForHourMinutes,
} from "~/utils/Functions/FormatDates";
import {
  ScheduleDatesRequestsProps,
  ScheduleShowProps,
} from "~/utils/Types/Admin/Schedule";
import { useCan } from "~/hooks/useCan";

export interface ScheduleDetailsProps extends BoxProps {
  appointmentSecureId?: string;
  scheduleSecureId?: string;
  showActionLog?: boolean;
  dataSource?: "appointment" | "schedule";
}

const ScheduleDetailsIntermediate: FC<ScheduleDetailsProps> = ({
  appointmentSecureId,
  scheduleSecureId,
  showActionLog = false,
  dataSource = "schedule",
  ...boxProps
}) => {
  const idToFetch =
    dataSource === "schedule" ? appointmentSecureId : scheduleSecureId;
  const endpoint =
    dataSource === "schedule"
      ? `/v1/admin/appointments/${idToFetch}`
      : `/v1/admin/schedules/${idToFetch}`;

  const { data: scheduleData } = useQuery(
    [dataSource, idToFetch],
    async () => {
      if (!idToFetch) return null;

      const response = await api.get(endpoint);

      return response.data as ScheduleShowProps;
    },
    {
      enabled: !!idToFetch,
    }
  );

  const checkDateType = useCallback((data: ScheduleDatesRequestsProps) => {
    if (data.date_type === "period") {
      switch (data.value) {
        case "morning":
          return "Manhã";
        case "afternoon":
          return "Tarde";
        case "night":
          return "Noite";
        default:
          return "";
      }
    }
    return FormatDateForHourMinutes(data.date);
  }, []);

  const checkStatus = useCallback((data: ScheduleDatesRequestsProps) => {
    switch (data.status) {
      case "to_check":
        return { text: "Checar", color: "orange.400" };
      case "unavailable":
        return { text: "Indisponível", color: "red" };
      case "available":
        return { text: "Disponível", color: "green" };
      default:
        return { text: "", color: "" };
    }
  }, []);

  if (!scheduleData) {
    return (
      <Alert status="warning">
        <AlertIcon />
        Agendamento não encontrado
      </Alert>
    );
  }

  return (
    <>
      <VStack {...boxProps} w={"100%"} mt={8}>
        <Stack w={"100%"} rounded={"md"} shadow={"xs"} padding={4} bg={"white"}>
          <Grid
            templateColumns={{
              sm: "repeat(4, 1fr)",
              md: "repeat(8, 1fr)",
              lg: "repeat(10, 1fr)",
              xl: "repeat(12, 1fr)",
              "2xl": "repeat(12, 1fr)",
            }}
            gap={6}
          >
            {/* Solicitação */}
            <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
              <Text fontSize="xs">Solicitação</Text>
              <Divider />
              <HStack>
                <Text fontSize="sm" fontWeight="medium">
                  {scheduleData?.type_consult !== "exam"
                    ? "Consulta com:"
                    : "Exame:"}
                </Text>
                <Text>
                  {scheduleData?.specialty?.name || ""}
                  {scheduleData?.exam?.name || ""}
                </Text>
              </HStack>
            </GridItem>

            {/* Paciente */}
            <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
              <Text fontSize="xs">Paciente</Text>
              <Divider />
              <HStack>
                <Text fontSize="sm" fontWeight="medium">
                  Nome:{" "}
                </Text>
                <Text>{scheduleData?.patient?.userInfo?.name || "N/A"}</Text>
              </HStack>
              {scheduleData?.patient?.userInfo?.phone && (
                <HStack>
                  <Text fontSize="sm" fontWeight="medium">
                    Telefone:{" "}
                  </Text>
                  <Text>
                    ({scheduleData.patient.userInfo.ddd_phone}) -{" "}
                    {scheduleData.patient.userInfo.phone}
                  </Text>
                </HStack>
              )}
              {scheduleData?.patient?.userInfo?.cell && (
                <HStack>
                  <Text fontSize="sm" fontWeight="medium">
                    Celular:{" "}
                  </Text>
                  <Text>
                    ({scheduleData.patient.userInfo.ddd_cell}) -{" "}
                    {scheduleData.patient.userInfo.cell}
                  </Text>
                </HStack>
              )}
            </GridItem>

            {/* Responsável (se existir) */}
            {scheduleData.patient?.parent && (
              <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
                <Stack>
                  <Text fontSize="xs">Responsável</Text>
                  <Divider />
                  <HStack>
                    <Text fontSize="sm" fontWeight="medium">
                      Nome:{" "}
                    </Text>
                    <Text>{scheduleData.patient.parent.userInfo.name}</Text>
                  </HStack>
                  {scheduleData.patient.parent.userInfo.phone && (
                    <HStack>
                      <Text fontSize="sm" fontWeight="medium">
                        Telefone:{" "}
                      </Text>
                      <Text>
                        ({scheduleData.patient.parent.userInfo.ddd_phone}) -{" "}
                        {scheduleData.patient.parent.userInfo.phone}
                      </Text>
                    </HStack>
                  )}
                  {scheduleData.patient.parent.userInfo.cell && (
                    <HStack>
                      <Text fontSize="sm" fontWeight="medium">
                        Celular:{" "}
                      </Text>
                      <Text>
                        ({scheduleData.patient.parent.userInfo.ddd_cell}) -{" "}
                        {scheduleData.patient.parent.userInfo.cell}
                      </Text>
                    </HStack>
                  )}
                </Stack>
              </GridItem>
            )}

            {/* Local */}
            <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
              <Text fontSize="xs">Local</Text>
              <Divider />
              <HStack>
                <Text fontSize="sm" fontWeight="medium">
                  Bairro:{" "}
                </Text>
                <Text>{scheduleData?.schedule?.neighborhood || ""}</Text>
              </HStack>
              <HStack>
                <Text fontSize="sm" fontWeight="medium">
                  Cidade:{" "}
                </Text>
                <Text>{scheduleData?.schedule?.city || ""}</Text>
              </HStack>
              <HStack>
                <Text fontSize="sm" fontWeight="medium">
                  Estado:{" "}
                </Text>
                <Text>{scheduleData?.schedule?.state || ""}</Text>
              </HStack>
            </GridItem>

            {/* Uploads (se existirem) */}
            {scheduleData.uploads && scheduleData.uploads.length > 0 && (
              <GridItem colSpan={{ sm: 4, md: 8, lg: 5, xl: 6, "2xl": 6 }}>
                <Text fontSize="xs">Solicitação de Exame</Text>
                <Divider />
                {scheduleData.uploads.map((item) => (
                  <Link href={item.url} target="_blank" key={item.url}>
                    <Image src={item.url} w={14} h={16} />
                  </Link>
                ))}
              </GridItem>
            )}
          </Grid>
        </Stack>
      </VStack>

      <VStack {...boxProps} w={"100%"} mt={4}>
        {/* Tabela de agendamentos */}
        {scheduleData.schedule?.scheduleDatesRequests &&
          scheduleData.schedule?.scheduleDatesRequests.length > 0 && (
            <Box
              w="100%"
              rounded="md"
              shadow="md"
              padding={4}
              bg={"white"}
            >
              <TableContainer>
								<Table variant='simple'>
									<Thead>
										<Tr>
											<Th>Médico / Clinica / Laboratório</Th>
											<Th>Telefone</Th>
											<Th>Data</Th>
											<Th>Horário</Th>
											<Th>Disponibilidade</Th>
										</Tr>
									</Thead>
									<Tbody>
										{scheduleData.schedule?.scheduleDatesRequests.map((item) => (
											<Tr key={item.secure_id}>
												<Td>
													{item.partner && item.partner.userInfo
														? item.partner.userInfo.name
														: "helloMed"}
												</Td>
												<Td>
													{item.partner && item.partner.userInfo
														? ` (${item.partner.userInfo.ddd_cell})${item.partner.userInfo.cell}`
														: ""}
												</Td>
												<Td>{FormatDateForDayMonthYearUsingBars(item.date)}</Td>
												<Td>{checkDateType(item)}</Td>
												<Td color={checkStatus(item).color}>
													{checkStatus(item).text}
												</Td>
											</Tr>
										))}
									</Tbody>
								</Table>
              </TableContainer>
            </Box>
          )}
      </VStack>
    </>
  );
};

export const ScheduleDetails: FC<ScheduleDetailsProps> = (props) => {
  const userHasPermissionView = useCan({
    permissions: ["schedule_view"],
  });

  if (!userHasPermissionView) {
    return (
      <Alert status="warning">
        <AlertIcon />
        Você não tem permissão para visualizar este {"schedule"}
      </Alert>
    );
  }

  return <ScheduleDetailsIntermediate {...props} />;
};
