import { useQuery } from "@tanstack/react-query";
import { api } from "~/services/apiClient";

type GetDependencyCityByStateUFProps = {
  stateUF?: string; 
}

type DependenciesGetCityByEstadoUFResponse = {
  cities: { name: string; }[];
}

export type GetDependencyCityByStateUFResponse = {
  cities: { label: string; value: string }[];
}

export async function getDependencyCityByStateUF({ stateUF }: GetDependencyCityByStateUFProps): Promise<GetDependencyCityByStateUFResponse> {
  if (!stateUF) {
    return { cities: [] };
  }

  const response = await api.get<DependenciesGetCityByEstadoUFResponse>(
    `/v1/admin/accrediteds-dependencies-get-city/${stateUF}`
  );

  const formattedCities = (response?.data?.cities || []).map((city) => ({
    label: city.name,
    value: city.name,
  }));

  return {
    cities: [
      { label: 'Selecione uma cidade', value: 'selecione uma cidade' },
      ...formattedCities,
    ],
  };
}

export function useAccreditedsDependenciesGetCityByEstadoUF({ stateUF }: GetDependencyCityByStateUFProps) {
  const isStateSelected = !!stateUF && stateUF.toLowerCase() !== 'selecione um estado';

  return useQuery(
    ['AccreditedsDependenciesGetCityByEstadoUF', stateUF], 
    () => getDependencyCityByStateUF({ stateUF }),
    {
      enabled: isStateSelected,
    }
  );
}
